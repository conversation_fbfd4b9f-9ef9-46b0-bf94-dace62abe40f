import React, { useRef, Suspense } from 'react';
import { motion } from 'framer-motion';
import { Mail, MapPin, Heart, Monitor } from 'lucide-react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, useTexture } from '@react-three/drei';
import * as THREE from 'three';
import { cn } from '@/lib/utils'; // Assuming this utility exists for classnames

// --- TYPE DEFINITIONS ---
// Assuming BaseSectionProps is defined elsewhere, e.g.:
// export interface BaseSectionProps {
//   className?: string;
// }
type BaseSectionProps = {
  className?: string;
};

// --- DEPENDENCY NOTE ---
// This component uses @react-three/fiber and @react-three/drei for the interactive globe.
// Please ensure you have these dependencies installed in your project:
// npm install three @react-three/fiber @react-three/drei
// or
// yarn add three @react-three/fiber @react-three/drei

// --- DATA ---
const technologies = [
    { name: "TypeScript", color: "#3178c6" },
    { name: "TailwindCSS", color: "#06b6d4" },
    { name: "Motion", color: "#8b5cf6" },
    { name: "PostgreSQL", color: "#336791" },
    { name: "MongoDB", color: "#47a248" },
    { name: "Prisma", color: "#2d3748" },
    { name: "Zustand", color: "#7b3f00" },
    { name: "pnpm", color: "#f69220" },
    { name: "Bun", color: "#fbf0df" },
    { name: "Git", color: "#f05032" },
    { name: "GitHub", color: "#ffffff" },
];


// --- REUSABLE BENTO CARD COMPONENT ---
// Enhanced with a hover glow effect for a premium feel.
const BentoCard = ({ className, children, delay = 0 }: { className?: string; children: React.ReactNode; delay?: number }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.6, delay }}
    className={cn(
      "group relative flex flex-col bg-neutral-900/80 border border-neutral-800 rounded-2xl p-6 backdrop-blur-xl overflow-hidden",
      className
    )}
  >
    {/* Inner Glow Effect */}
    <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
    <div className="absolute -inset-px rounded-2xl border border-transparent group-hover:border-neutral-700 transition-all duration-300" />
    
    <div className="relative z-10 flex flex-col h-full">
        {children}
    </div>
  </motion.div>
);


// --- 3D INTERACTIVE GLOBE ---
function Earth() {
  const earthRef = useRef<THREE.Mesh>(null!);
  
  // High-quality textures for the Earth model
  const [colorMap, normalMap, specularMap] = useTexture([
    'https://eoimages.gsfc.nasa.gov/images/imagerecords/73000/73909/world.topo.bathy.200412.3x5400x2700.jpg',
    'https://eoimages.gsfc.nasa.gov/images/imagerecords/73000/73909/world.topo.bathy.200412.3x5400x2700.jpg', // Using the same for normal map for subtle effect
    'https://eoimages.gsfc.nasa.gov/images/imagerecords/57000/57735/land_ocean_ice_8192.png' // Specular map for water reflection
  ]);

  // Auto-rotation animation
  useFrame(() => {
    if (earthRef.current) {
      earthRef.current.rotation.y += 0.0005;
    }
  });

  return (
    <mesh ref={earthRef} scale={2.5}>
      <sphereGeometry args={[1, 64, 64]} />
      <meshStandardMaterial
        map={colorMap}
        normalMap={normalMap}
        specularMap={specularMap}
        metalness={0.2}
        roughness={0.7}
      />
    </mesh>
  );
}

const InteractiveGlobe = () => (
  <div className="w-full h-full min-h-[250px] cursor-grab active:cursor-grabbing">
    <Canvas camera={{ position: [0, 0, 3.5], fov: 45 }}>
      <Suspense fallback={null}>
        <ambientLight intensity={0.2} />
        <directionalLight position={[5, 5, 5]} intensity={1.5} />
        <Earth />
        <OrbitControls enableZoom={false} enablePan={false} autoRotate={false} />
      </Suspense>
    </Canvas>
  </div>
);


// --- BENTO GRID CARD COMPONENTS ---

const HeroCard = () => (
  <BentoCard className="md:col-span-7 items-center justify-center text-center">
    <div className="relative flex items-center justify-center mb-6">
        {/* Concentric circles for a modern avatar background */}
        {[...Array(3)].map((_, i) => (
            <div
                key={i}
                className="absolute w-40 h-40 md:w-48 md:h-48 rounded-full border border-white/10"
                style={{ transform: `scale(${1 + i * 0.3})`, opacity: 1 - i * 0.3 }}
            />
        ))}
        <img 
            src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?q=80&w=2080&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
            alt="User Avatar"
            className="w-24 h-24 rounded-full object-cover border-2 border-neutral-800"
        />
    </div>
    <div className="flex items-center gap-2 text-sm text-neutral-400 mb-2 justify-center">
      <Heart className="w-4 h-4" />
      <span>Collaboration</span>
    </div>
    <h3 className="text-xl md:text-2xl font-semibold text-white leading-tight">
      I prioritize client collaboration, fostering open communication
    </h3>
  </BentoCard>
);

const TechStackCard = () => (
  <BentoCard className="md:col-span-5" delay={0.1}>
    <h3 className="text-lg font-semibold text-white mb-4">
      Passionate about cutting-edge technologies
    </h3>
    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
      {technologies.map((tech, index) => (
        <motion.div
          key={tech.name}
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.3, delay: index * 0.05 }}
          className="flex items-center gap-2 px-3 py-2 bg-neutral-800/50 border border-neutral-700/80 rounded-lg text-sm text-neutral-300 hover:bg-neutral-800 transition-colors"
        >
          <div className="w-2 h-2 rounded-full" style={{ backgroundColor: tech.color }} />
          {tech.name}
        </motion.div>
      ))}
    </div>
  </BentoCard>
);

const TimezoneCard = () => (
  <BentoCard className="md:col-span-4" delay={0.2}>
    <div className="w-full">
        <h3 className="text-xl font-semibold text-white">I'm very flexible with time</h3>
        <p className="text-blue-400 mb-4">zone communications</p>
        <div className="flex gap-2 mb-4">
          {["GB UK", "IN India", "US USA"].map((zone) => (
            <div key={zone} className="px-3 py-1 bg-neutral-800/50 border border-neutral-700/80 rounded-md text-xs text-neutral-300">
              {zone}
            </div>
          ))}
        </div>
    </div>
    <div className="flex-grow flex items-center justify-center -mx-6 -mb-6">
        <InteractiveGlobe />
    </div>
    <div className="flex items-center gap-2 text-sm text-white mt-auto pt-4">
      <MapPin className="w-4 h-4" />
      <span>Remote</span>
      <span className="text-neutral-600">/</span>
      <span className="text-blue-400">India</span>
    </div>
  </BentoCard>
);

const CollaborationCard = () => (
  <BentoCard className="md:col-span-4 justify-between items-center text-center" delay={0.3}>
    <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center mb-4">
      <span className="text-white font-bold text-4xl">AB</span>
    </div>
    <div>
        <h3 className="text-xl font-semibold text-white">Let's work together</h3>
        <p className="text-neutral-400 mb-4">on your next project</p>
    </div>
    <button className="w-full flex items-center justify-center gap-2 px-4 py-2.5 bg-neutral-800/50 border border-neutral-700/80 hover:bg-neutral-800 rounded-lg text-white text-sm transition-colors mt-auto">
      <Mail className="w-4 h-4" />
      <EMAIL>
    </button>
  </BentoCard>
);

const PortfolioPreviewCard = () => (
  <BentoCard className="md:col-span-4" delay={0.4}>
    <div className="bg-neutral-800/50 rounded-lg p-3 h-full flex flex-col">
      <div className="flex items-center gap-1.5 mb-3">
        <div className="w-3 h-3 rounded-full bg-red-500/80"></div>
        <div className="w-3 h-3 rounded-full bg-yellow-500/80"></div>
        <div className="w-3 h-3 rounded-full bg-green-500/80"></div>
      </div>
      <div className="bg-black/40 rounded-md p-4 text-center flex-grow flex flex-col justify-center">
        <p className="text-neutral-400 text-sm mb-3">
          Websites that stand out and make a difference
        </p>
        <div className="flex gap-2 justify-center">
          <button className="px-4 py-1.5 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded-md transition-colors">
            View Work
          </button>
          <button className="px-4 py-1.5 bg-neutral-700 hover:bg-neutral-600 text-white text-xs rounded-md transition-colors">
            Portfolio
          </button>
        </div>
      </div>
    </div>
  </BentoCard>
);


// --- MAIN SECTION COMPONENT ---
export default function CurrentFocusSection({ className }: BaseSectionProps) {
  return (
    <section className={cn('py-20 lg:py-32 text-white', className)} id="current-focus">
      <div className="container mx-auto px-4">
        {/* New 5-Card Bento Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-12 gap-6 max-w-7xl mx-auto">
          <HeroCard />
          <TechStackCard />
          <TimezoneCard />
          <CollaborationCard />
          <PortfolioPreviewCard />
        </div>
      </div>
    </section>
  );
}
